import React, { useState, useContext } from 'react';
import {
    View,
    Text,
    ScrollView,
    TouchableOpacity,
    StyleSheet,
    SafeAreaView,
    Dimensions,
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import { ThemeContext } from '../../context/ThemeContext';
import { getDashboardStats } from '../../api/admin/adminApi';
import { useQuery } from '@tanstack/react-query';
import queryClient from '../../api/queryClient';
import { showToast } from '../../utils/showToast';

const { width } = Dimensions.get('window');

const AdminDashboard = () => {
    const { theme } = useContext(ThemeContext);
    const router = useRouter();
    const [selectedPeriod, setSelectedPeriod] = useState('month');

    // const {
    //     data: adminStats,
    //     isLoading,
    //     error,
    // } = useQuery({
    //     queryKey: ['admin-stats'],
    //     queryFn: getDashboardStats,
    //     refetchInterval: 30000,
    //     refetchIntervalInBackground: false,
    //     onError: () => {
    //         showToast('error', 'Error', 'Failed to fetch admin stats.');
    //     },
    // });
    // Mock admin data
    const adminStats = {
        totalUsers: 12450,
        activeUsers: 8920,
        totalProperties: 5680,
        activeListings: 3240,
        totalContractors: 890,
        verifiedContractors: 720,
        totalBrokers: 340,
        verifiedBrokers: 280,
        monthlyRevenue: 2850000,
        totalRevenue: 15600000,
        pendingVerifications: 45,
        reportedIssues: 12,
    };

    const recentActivities = [
        {
            id: 1,
            type: 'user',
            title: 'New user registration',
            description: '25 new users joined today',
            time: '2 hours ago',
            icon: 'person-add',
            color: '#4CAF50',
        },
        {
            id: 2,
            type: 'verification',
            title: 'Contractor verified',
            description: 'ABC Construction verified successfully',
            time: '4 hours ago',
            icon: 'checkmark-circle',
            color: '#2196F3',
        },
        {
            id: 3,
            type: 'payment',
            title: 'Payment processed',
            description: '₹1,25,000 in transactions today',
            time: '6 hours ago',
            icon: 'wallet',
            color: '#FF9800',
        },
        {
            id: 4,
            type: 'report',
            title: 'Issue reported',
            description: 'Property listing flagged for review',
            time: '1 day ago',
            icon: 'flag',
            color: '#F44336',
        },
    ];

    const quickActions = [
        {
            id: 'user-management',
            title: 'User Management',
            icon: 'people',
            color: '#4CAF50',
            onPress: () => router.push('/Admin/UserManagement'),
        },
        {
            id: 'property-management',
            title: 'Property Management',
            icon: 'home',
            color: '#2196F3',
            onPress: () => router.push('/Admin/PropertyManagement'),
        },
        {
            id: 'verification',
            title: 'Verifications',
            icon: 'shield-checkmark',
            color: '#FF9800',
            onPress: () => router.push('/Admin/Verifications'),
        },
        {
            id: 'reports',
            title: 'Reports & Analytics',
            icon: 'analytics',
            color: '#9C27B0',
            onPress: () => router.push('/Admin/Reports'),
        },
        {
            id: 'payments',
            title: 'Payment Management',
            icon: 'card',
            color: '#607D8B',
            onPress: () => router.push('/Admin/PaymentManagement'),
        },
        {
            id: 'support',
            title: 'Support Tickets',
            icon: 'help-circle',
            color: '#795548',
            onPress: () => router.push('/Admin/SupportTickets'),
        },
    ];

    const stats = [
        {
            title: 'Total Users',
            value: adminStats.totalUsers,
            subtitle: 'Active users',
            icon: 'people',
            color: '#4CAF50',
            trend: 12,
        },
        {
            title: 'Pending Verificatons',
            value: adminStats.pendingVerifications,
            subtitle: 'Pending',
            icon: 'shield-checkmark',
            color: '#2196F3',
            trend: 8,
        },
        {
            title: 'Contractors',
            value: adminStats.totalContractors,
            subtitle: 'Verified',
            icon: 'hammer',
            color: '#FF9800',
            trend: 15,
        },
        {
            title: 'Brokers',
            value: adminStats.totalBrokers,
            subtitle: 'Verified',
            icon: 'briefcase',
            color: '#9C27B0',
            trend: 10,
        },
        // {
        //     title: 'Revenue',
        //     value: `₹${(adminStats.monthlyRevenue / 100000).toFixed(1)}L`,
        //     subtitle: 'This month',
        //     icon: 'wallet',
        //     color: '#607D8B',
        //     trend: 22,
        // },
        {
            title: 'Properties',
            value: adminStats.totalProperties,
            subtitle: 'Active listings',
            icon: 'home',
            color: '#2196F3',
            trend: 8,
        },
        // {
        //     title: 'Transactions',
        //     value: adminStats.totalTransactions,
        //     subtitle: 'This month',
        //     icon: 'card',
        //     color: '#795548',
        //     trend: 18,
        // },
        {
            title: 'Active Tickets',
            value: adminStats.activeTickets,
            subtitle: 'Pending',
            icon: 'help-circle',
            color: '#795548',
            trend: 12,
        },
    ];

    const periods = [
        { id: 'week', name: 'This Week' },
        { id: 'month', name: 'This Month' },
        { id: 'quarter', name: 'This Quarter' },
        { id: 'year', name: 'This Year' },
    ];

    const renderStatCard = ({ title, value, subtitle, icon, color, trend }) => (
        <View style={[styles.statCard, { backgroundColor: theme.CARD }]}>
            <View style={styles.statHeader}>
                <View
                    style={[styles.statIcon, { backgroundColor: color + '20' }]}
                >
                    <Ionicons name={icon} size={24} color={color} />
                </View>
                {trend && (
                    <View style={styles.trendContainer}>
                        <Ionicons
                            name={trend > 0 ? 'trending-up' : 'trending-down'}
                            size={16}
                            color={trend > 0 ? '#4CAF50' : '#F44336'}
                        />
                        <Text
                            style={[
                                styles.trendText,
                                { color: trend > 0 ? '#4CAF50' : '#F44336' },
                            ]}
                        >
                            {Math.abs(trend)}%
                        </Text>
                    </View>
                )}
            </View>
            <Text style={[styles.statValue, { color: theme.TEXT_PRIMARY }]}>
                {value}
            </Text>
            <Text style={[styles.statTitle, { color: theme.TEXT_SECONDARY }]}>
                {title}
            </Text>
            {subtitle && (
                <Text
                    style={[
                        styles.statSubtitle,
                        { color: theme.TEXT_SECONDARY },
                    ]}
                >
                    {subtitle}
                </Text>
            )}
        </View>
    );

    const renderActivity = (activity) => (
        <TouchableOpacity
            key={activity.id}
            style={[styles.activityCard, { backgroundColor: theme.CARD }]}
        >
            <View
                style={[
                    styles.activityIcon,
                    { backgroundColor: activity.color + '20' },
                ]}
            >
                <Ionicons
                    name={activity.icon}
                    size={20}
                    color={activity.color}
                />
            </View>
            <View style={styles.activityContent}>
                <Text
                    style={[
                        styles.activityTitle,
                        { color: theme.TEXT_PRIMARY },
                    ]}
                >
                    {activity.title}
                </Text>
                <Text
                    style={[
                        styles.activityDescription,
                        { color: theme.TEXT_SECONDARY },
                    ]}
                >
                    {activity.description}
                </Text>
                <Text
                    style={[
                        styles.activityTime,
                        { color: theme.TEXT_SECONDARY },
                    ]}
                >
                    {activity.time}
                </Text>
            </View>
        </TouchableOpacity>
    );

    const renderQuickAction = (action) => (
        <TouchableOpacity
            key={action.id}
            style={[styles.actionCard, { backgroundColor: theme.CARD }]}
            onPress={action.onPress}
        >
            <View
                style={[
                    styles.actionIcon,
                    { backgroundColor: action.color + '20' },
                ]}
            >
                <Ionicons name={action.icon} size={28} color={action.color} />
            </View>
            <Text style={[styles.actionTitle, { color: theme.TEXT_PRIMARY }]}>
                {action.title}
            </Text>
        </TouchableOpacity>
    );

    return (
        <SafeAreaView
            style={[styles.container, { backgroundColor: theme.BACKGROUND }]}
        >
            {/* Header */}
            <LinearGradient
                colors={[theme.PRIMARY, theme.SECONDARY]}
                style={styles.header}
            >
                <View style={styles.headerContent}>
                    <TouchableOpacity onPress={() => router.back()}>
                        <Ionicons name="arrow-back" size={24} color="#fff" />
                    </TouchableOpacity>
                    <Text style={styles.headerTitle}>Admin Dashboard</Text>
                    <TouchableOpacity
                        onPress={() => router.push('/Admin/AdminSettings')}
                    >
                        <Ionicons name="settings" size={24} color="#fff" />
                    </TouchableOpacity>
                </View>
            </LinearGradient>

            <ScrollView
                style={styles.content}
                showsVerticalScrollIndicator={false}
            >
                {/* Period Selector */}
                <View style={styles.periodSection}>
                    <ScrollView
                        horizontal
                        showsHorizontalScrollIndicator={false}
                        contentContainerStyle={styles.periodContainer}
                    >
                        {periods.map((period) => (
                            <TouchableOpacity
                                key={period.id}
                                style={[
                                    styles.periodButton,
                                    {
                                        backgroundColor:
                                            selectedPeriod === period.id
                                                ? theme.PRIMARY + '66'
                                                : theme.BACKGROUND,
                                        borderWidth: 1,
                                        borderColor: theme.PRIMARY,
                                    },
                                ]}
                                onPress={() => setSelectedPeriod(period.id)}
                            >
                                <Text
                                    style={[
                                        styles.periodText,
                                        {
                                            color:
                                                selectedPeriod === period.id
                                                    ? '#fff'
                                                    : theme.TEXT_SECONDARY,
                                        },
                                    ]}
                                >
                                    {period.name}
                                </Text>
                            </TouchableOpacity>
                        ))}
                    </ScrollView>
                </View>

                {/* Statistics Cards */}
                <View style={styles.statsSection}>
                    <Text
                        style={[
                            styles.sectionTitle,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Overview Statistics
                    </Text>
                    <View style={styles.statsGrid}>
                        {stats.map(renderStatCard)}
                    </View>
                </View>

                {/* Quick Actions */}
                <View style={styles.actionsSection}>
                    <Text
                        style={[
                            styles.sectionTitle,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Quick Actions
                    </Text>
                    <View style={styles.actionsGrid}>
                        {quickActions.map(renderQuickAction)}
                    </View>
                </View>

                {/* Recent Activities */}
                <View style={styles.activitiesSection}>
                    <Text
                        style={[
                            styles.sectionTitle,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Recent Activities
                    </Text>
                    <View style={styles.activitiesList}>
                        {recentActivities.map(renderActivity)}
                    </View>
                </View>

                <View style={{ height: 20 }} />
            </ScrollView>
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    header: {
        paddingTop: 50,
        paddingBottom: 20,
        paddingHorizontal: 20,
    },
    headerContent: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
    },
    headerTitle: {
        fontSize: 20,
        fontWeight: 'bold',
        color: '#fff',
    },
    content: {
        flex: 1,
    },
    periodSection: {
        paddingVertical: 16,
    },
    periodContainer: {
        paddingHorizontal: 16,
        gap: 12,
    },
    periodButton: {
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 20,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    periodText: {
        fontSize: 14,
        fontWeight: '600',
    },
    statsSection: {
        padding: 16,
    },
    sectionTitle: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 16,
    },
    statsGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: 12,
    },
    statCard: {
        flex: 1,
        minWidth: (width - 48) / 2,
        padding: 16,
        borderRadius: 12,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    statHeader: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: 12,
    },
    statIcon: {
        width: 40,
        height: 40,
        borderRadius: 20,
        alignItems: 'center',
        justifyContent: 'center',
    },
    trendContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 4,
    },
    trendText: {
        fontSize: 12,
        fontWeight: '600',
    },
    statValue: {
        fontSize: 20,
        fontWeight: 'bold',
        marginBottom: 4,
    },
    statTitle: {
        fontSize: 14,
        fontWeight: '600',
    },
    statSubtitle: {
        fontSize: 12,
        marginTop: 2,
    },
    actionsSection: {
        padding: 16,
    },
    actionsGrid: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        gap: 12,
    },
    actionCard: {
        flex: 1,
        minWidth: (width - 48) / 2,
        padding: 16,
        borderRadius: 12,
        alignItems: 'center',
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    actionIcon: {
        width: 50,
        height: 50,
        borderRadius: 25,
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 8,
    },
    actionTitle: {
        fontSize: 12,
        fontWeight: '600',
        textAlign: 'center',
    },
    activitiesSection: {
        padding: 16,
    },
    activitiesList: {
        gap: 12,
    },
    activityCard: {
        flexDirection: 'row',
        padding: 16,
        borderRadius: 12,
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
    },
    activityIcon: {
        width: 40,
        height: 40,
        borderRadius: 20,
        alignItems: 'center',
        justifyContent: 'center',
        marginRight: 12,
    },
    activityContent: {
        flex: 1,
    },
    activityTitle: {
        fontSize: 14,
        fontWeight: '600',
        marginBottom: 4,
    },
    activityDescription: {
        fontSize: 12,
        marginBottom: 4,
    },
    activityTime: {
        fontSize: 11,
    },
});

export default AdminDashboard;
